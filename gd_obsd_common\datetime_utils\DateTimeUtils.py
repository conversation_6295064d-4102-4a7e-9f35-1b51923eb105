from datetime import datetime, timedelta
import pytz
from datetime import timezone

class DateTimeUtils:
    """Utility class for common date and time operations."""

    @staticmethod
    def str_to_datetime(date_str, fmt='%Y-%m-%d %H:%M:%S'):
        """Convert string to datetime object."""
        return datetime.strptime(date_str, fmt)

    @staticmethod
    def datetime_to_str(date_obj, fmt='%Y-%m-%d %H:%M:%S'):
        """Convert datetime object to string."""
        return date_obj.strftime(fmt)

    @staticmethod
    def add_days(date_obj, days):
        """Add (or subtract) days to a datetime object."""
        return date_obj + timedelta(days=days)

    @staticmethod
    def convert_timezone(date_obj, from_tz, to_tz):
        """Convert a datetime object from one timezone to another."""
        from_zone = pytz.timezone(from_tz)
        to_zone = pytz.timezone(to_tz)
        localized = from_zone.localize(date_obj)
        return localized.astimezone(to_zone)

    @staticmethod
    def get_current_utc():
        """Get the current UTC datetime."""
        return datetime.now(timezone.utc)


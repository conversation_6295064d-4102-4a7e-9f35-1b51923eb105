# GD OBSD Common Library

A Python package providing common functionality shared across OBSD streams in the GovDigital platform.

## Introduction

This library contains reusable utilities and components to standardize common operations across OBSD services. It currently includes:

- **DateTime Utilities**: Standardized date/time handling and conversions
- **SharePoint Integration**: Simplified access to SharePoint resources via Microsoft Graph API

## Installation

### From Azure DevOps Feed

```bash
# Install the package
pip install gd-obsd-common
```

### For Development

```bash
# Clone the repository
git clone https://dev.azure.com/ADGOV/GovDigital/_git/gd-obsd-common
cd gd-obsd-common

# Install in development mode with dev dependencies
pip install -e ".[dev]"
```

## Requirements

- Python 3.12 or higher
- Core dependencies:
  - pytz
  - azure-identity
  - msgraph-sdk
  - requests
  - pandas
  - pyspark (v3.5.0+)

## Usage Examples

### DateTime Utilities

```python
from gd_obsd_common import DateTimeUtils

# Initialize the utility
dt_utils = DateTimeUtils()

# Convert string to datetime
date_str = '2025-05-07 15:12:00'
dt = dt_utils.str_to_datetime(date_str)
print("Datetime object:", dt)

# Format datetime to string
formatted = dt_utils.datetime_to_str(dt)
print("Formatted date:", formatted)

# Add days to a date
future_date = dt_utils.add_days(dt, 7)
print("Date + 7 days:", future_date)
```

### SharePoint Integration

```python
from gd_obsd_common import SharePoint

# Initialize SharePoint client
sp = SharePoint()

# Read file content from SharePoint
file_url = "https://adgov.sharepoint.com/sites/your-site/Shared%20Documents/path/to/file.xlsx"
file_content = sp.read_file_from_url(file_url)

# Get lists from a SharePoint site
site_url = "https://adgov.sharepoint.com/sites/your-site"
lists = sp.get_lists(site_url, output_type="table")

# Get content from a specific list
list_id = "your-list-id"
list_content = sp.get_list_content(site_url, list_id, output_type="table")
```

## Contributing

### Adding New Modules

When developing new common functionality:

1. Create a new folder for your module under `gd_obsd_common/`
2. Create proper `__init__.py` files to ensure correct imports
3. Add any new dependencies to `requirements.txt` and `pyproject.toml`
4. Update the main `gd_obsd_common/__init__.py` to expose your module
5. Add appropriate tests in the `tests/` directory

### Module Structure

```
gd_obsd_common/
├── new_module/
│   ├── __init__.py         # Import and expose your module's classes
│   └── NewModule.py        # Implementation
├── __init__.py             # Update to include: from .new_module import NewModule
```

### Important Notes

- Ensure your module name is correctly referenced in all files (singular `gd_obsd_common`, not plural)
- Follow the existing code style and patterns
- Add comprehensive docstrings and comments
- Write unit tests for all new functionality

### Development Workflow

1. Set up your development environment: `pip install -e ".[dev]"`
2. Install pre-commit hooks: `pre-commit install`
3. Make your changes
4. Run tests: `pytest`
5. Check coverage: `coverage run --source=gd_obsd_common/ --branch -m pytest tests/ && coverage report`
6. Submit your changes for review

## Quality Assurance

This project uses several tools to maintain code quality:

- **pre-commit**: Runs checks before each commit
- **flake8**: Enforces PEP 8 style guide (max line length: 120)
- **isort**: Sorts imports alphabetically
- **bandit**: Finds common security issues
- **pytest**: Runs unit tests
- **coverage**: Measures test coverage

## CI/CD Pipeline

The project uses Azure DevOps pipelines for continuous integration and deployment:

1. Builds and tests the package
2. Generates a new version tag
3. Publishes the package to the organization's private feed

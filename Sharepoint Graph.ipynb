{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "373d2819-d5e0-4966-8fda-fe04bb5f7b06", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%python\n", "!pip install azure-identity \n", "!pip install msgraph-sdk\n", "# !pip install requests"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "0de1e505-a6be-420b-abc6-c8a0f7cfd6dd", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%python\n", "\n", "import logging\n", "import requests\n", "import time\n", "import pandas as pd\n", "import json\n", "from requests.exceptions import RequestException, ConnectionError, Timeout\n", "from azure.core.exceptions import ClientAuthenticationError\n", "import socket\n", "from azure.identity import ClientSecretCredential\n", "from msgraph import GraphServiceClient\n", "from pyspark.sql import SparkSession, Row, DataFrame\n", "from pyspark.sql.functions import col, explode\n", "from pyspark.sql.types import StructType, ArrayType\n", " \n", "class SharePoint:\n", "    \n", "    # Class to interact with SharePoint.\n", "    \n", "    def __init__(self) -> None:\n", "        self.hostname = \"adgov.sharepoint.com\"\n", "        # Initialize Microsoft Graph client\n", "        self.credentials = ClientSecretCredential(\n", "            tenant_id=\"0cefd05a-5b38-4ce7-96bb-c31e6e251d18\",\n", "            client_id=dbutils.secrets.get(scope=\"kv-app\", key=\"common-sharepoint-client-id\"),\n", "            client_secret=dbutils.secrets.get(scope=\"kv-app\", key=\"common-sharepoint-client-secret\")\n", "        )\n", "        scopes = ['https://graph.microsoft.com/.default']\n", "        self.graph_client = GraphServiceClient(self.credentials, scopes)\n", "        # Acquire token right after initialization\n", "        self._acquire_access_token()\n", "\n", "    def _acquire_access_token(self, max_attempts=5, base_delay=2.0):\n", "        attempt = 0\n", "        while attempt < max_attempts:\n", "            attempt += 1\n", "            try:\n", "                scopes = ['https://graph.microsoft.com/.default']\n", "                logging.info(\"Generate SharePoint access token\")\n", "                self.access_token = self.credentials.get_token(scopes[0]).token\n", "                logging.info(\"New access token acquired!\")\n", "                return\n", "            except (Client<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ConnectionResetError, socket.error) as e:\n", "                logging.warning(f\"Auth error: {repr(e)}\")\n", "                if attempt == max_attempts:\n", "                    raise\n", "                sleep_time = base_delay * (2 ** (attempt - 1))\n", "                logging.info(f\"Retrying after {sleep_time:.1f} seconds...\")\n", "                time.sleep(sleep_time)\n", "\n", "    def _make_msgraph_call(self, request_url: str, max_attempts: int = 5, base_delay: float = 2.0) -> requests.Response:\n", "  \n", "        # Makes Microsoft Graph API call with retry and token renewal.\n", "        \n", "        # :param request_url: Full MS Graph API URL\n", "        # :param max_attempts: Max number of attempts\n", "        # :param base_delay: Initial delay for exponential backoff in seconds\n", "        # :return: requests.Response object\n", "\n", "        attempt_count = 0\n", "\n", "        while attempt_count < max_attempts:\n", "            attempt_count += 1\n", "            try:\n", "                logging.info(f\"MS Graph call to URL: {request_url}. Attempt {attempt_count}/{max_attempts}\")\n", "                response = requests.get(\n", "                    request_url,\n", "                    headers={\n", "                        \"Authorization\": f\"Bearer {self.access_token}\",\n", "                        \"Accept\": \"application/json\",\n", "                        \"Content-Type\": \"application/json\"\n", "                    },\n", "                    timeout=30  # optional: prevent hanging\n", "                )\n", "\n", "                status_code = response.status_code\n", "\n", "                # Success\n", "                if status_code == 200:\n", "                    return response\n", "\n", "                # Token expired\n", "                elif status_code == 401:\n", "                    logging.warning(\"Access token expired. Renewing token.\")\n", "                    self._acquire_access_token()\n", "\n", "                # Redirect or temporary unavailability\n", "                elif status_code in (302, 503):\n", "                    logging.warning(f\"Temporary issue ({status_code}). Retrying...\")\n", "\n", "                # Forbidden: no point retrying\n", "                elif status_code == 403:\n", "                    raise Exception(\n", "                        f\"Access forbidden. Check app permissions. {status_code}: {response.text}\"\n", "                    )\n", "\n", "                # Not found: handled gracefully\n", "                elif status_code == 404:\n", "                    logging.warning(f\"Resource not found: {request_url}\")\n", "                    return response\n", "\n", "                # Other non-200 responses\n", "                else:\n", "                    logging.warning(f\"Received unexpected status code {status_code}: {response.text}\")\n", "\n", "            except (<PERSON><PERSON><PERSON><PERSON>, Timeout, RequestException) as e:\n", "                logging.warning(f\"Request error: {e}. Attempt {attempt_count}/{max_attempts}\")\n", "\n", "            # Wait before retrying\n", "            sleep_time = base_delay * (2 ** (attempt_count - 1))\n", "            logging.info(f\"Waiting {sleep_time:.1f} seconds before retrying...\")\n", "            time.sleep(sleep_time)\n", "\n", "        # Final failure\n", "        raise Exception(f\"MS Graph call to {request_url} failed after {max_attempts} attempts.\")   \n", " \n", "    def _get_site_id(self, url: str) -> str:\n", "        \n", "        # Retrieve site id from the given SharePoint url.\n", "        # :param url: Url to extract the site from.\n", "        # :return: site id\n", "      \n", "        # Extract site name from url\n", "        if '/teams/' in url:\n", "            site_name = url.split('/teams/')[1].split('/')[0]\n", "            endpoint = f\":/teams/{site_name}\"  # Teams sites require :/teams/ in endpoint\n", "        elif '/sites/' in url:\n", "            site_name = url.split('/sites/')[1].split('/')[0]\n", "            endpoint = f\":/sites/{site_name}\"  # Site sites require :/teams/ in endpoint\n", "        else:\n", "            endpoint = ''  # Default site\n", "        url = f\"https://graph.microsoft.com/v1.0/sites/{self.hostname}{endpoint}\"\n", "        response = self._make_msgraph_call(url)\n", "        site_id = response.json()[\"id\"]\n", "        logging.info(f\"Retrieved site id '{site_id}' from host '{self.hostname}' and site '{site_name}'\")\n", "\n", "        return site_id\n", " \n", "    def read_file_from_url(self, url: str) -> bytes:\n", "        \n", "        # Read SharePoint file content as bytes from the given url.\n", "        # :param url: SharePoint file's url.\n", "        # :return: content as bytes.\n", " \n", "        site_id = self._get_site_id(url)\n", " \n", "        relative_path = url.split(f\"/Shared%20Documents/\")[1].split('?')[0]\n", "        request_url = f'https://graph.microsoft.com/v1.0/sites/{site_id}/drive/root:/{relative_path}:/content'\n", " \n", "        response = self._make_msgraph_call(request_url)\n", "        if response.status_code == 404:\n", "            # If not found return None\n", "            raise FileNotFoundError(f\"File not found in given url {url}. Please review the url and make sure the file is present in the corresponding path. Full error: {response.text}\")\n", "        # Return file content as bytes\n", "        return response.content\n", "    \n", "    def get_lists(self, url: str, output_type: str=\"\") -> list:\n", "        \n", "        # Get all available lists from Sharepoint\n", "        \n", "        site_id = self._get_site_id(url)\n", "        \n", "        lists_response = requests.get(f\"https://graph.microsoft.com/v1.0/sites/{site_id}/lists\",\n", "                                      headers={\n", "                                          \"Authorization\": f\"Bearer {self.access_token}\",\n", "                                          \"Accept\": \"application/json\",\n", "                                          \"Content-Type\": \"application/json\"\n", "                                      })\n", "        lists = lists_response.json()\n", "        \n", "        if output_type == \"table\":\n", "            lists = SharePoint.flatten_df(lists)\n", "        elif output_type == \"json\":\n", "            lists = json.dumps(lists)\n", "        else:\n", "            pass\n", "            \n", "        return lists\n", "    \n", "    def get_list_content(self, url: str, list_id: str, output_type: str=\"\"):\n", "        \n", "        # Get list content.\n", "\n", "        def clean_value(value):\n", "            \"\"\"\n", "            Clean values to make them compatible with PySpark.\n", "            \"\"\"\n", "            import datetime\n", "\n", "            if isinstance(value, (int, float, bool, str, type(None))):\n", "                return value\n", "            elif isinstance(value, datetime.datetime):\n", "                return value.isoformat()\n", "            elif isinstance(value, list):\n", "                return str(value)\n", "            elif isinstance(value, dict):\n", "                return str(value)\n", "            else:\n", "                return str(value)\n", "\n", "        site_id = self._get_site_id(url)\n", "\n", "        items = [] \n", "        list_id = list_id\n", "        url = f\"https://graph.microsoft.com/v1.0/sites/{site_id}/lists/{list_id}/items?$expand=fields\"\n", "        while url:\n", "            items_response = requests.get(\n", "                url,\n", "                headers={\n", "                    \"Authorization\": f\"Bearer {self.access_token}\",\n", "                    \"Accept\": \"application/json\",\n", "                    \"Content-Type\": \"application/json\"\n", "                })\n", "\n", "            item = items_response.json()\n", "            items.extend(item.get('value', []))\n", "            url = item.get('@odata.nextLink', None)\n", "\n", "        data = [item['fields'] for item in items]\n", "\n", "        if output_type == \"table\":\n", "            output = SharePoint.flatten_df(data)\n", "        elif output_type == \"json\":\n", "            # output = json.dumps(items)\n", "            output = items\n", "        elif output_type == \"response\":\n", "            output = items_response\n", "        else:\n", "            pass\n", "\n", "        return output\n", "    \n", "# Function to Transform JSON into Table \n", "    @staticmethod\n", "    def flatten_df(lists: dict) -> DataFrame:\n", "        \n", "        # Recursively flattens a PySpark DataFrame with structs and arrays,\n", "        # handling column names with special characters.\n", "        \n", "        def safe_col(col_name: str):\n", "            \n", "            # Returns a safe `col` expression for column names with special characters.\n", "            \n", "            if any(c in col_name for c in ['.', '@', ' '] ):\n", "                return col(f\"`{col_name}`\")\n", "            else:\n", "                return col(col_name)\n", "\n", "        complex_fields = True\n", "\n", "        pdf = pd.json_normalize(lists)\n", "        df = spark.createDataFrame(pdf)\n", "\n", "        while complex_fields:\n", "            complex_fields = False\n", "            new_cols = []\n", "            for field in df.schema.fields:\n", "                field_name = field.name\n", "                field_type = field.dataType\n", "\n", "                if isinstance(field_type, StructType):\n", "                    # Decompose struct\n", "                    for subfield in field_type.fields:\n", "                        sub_name = subfield.name\n", "                        new_name = f\"{field_name}_{sub_name}\"\n", "                        new_cols.append(col(f\"`{field_name}`.`{sub_name}`\").alias(new_name))\n", "                    complex_fields = True\n", "\n", "                elif isinstance(field_type, ArrayType) and isinstance(field_type.elementType, StructType):\n", "                    # Explode array of structs\n", "                    df = df.withColumn(field_name, explode(safe_col(field_name)))\n", "                    complex_fields = True\n", "                    new_cols.append(safe_col(field_name))\n", "\n", "                else:\n", "                    new_cols.append(safe_col(field_name))\n", "\n", "            df = df.select(*new_cols)\n", "\n", "        return df   "]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "sql", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "Sharepoint Graph", "widgets": {}}, "language_info": {"name": "sql"}}, "nbformat": 4, "nbformat_minor": 0}
#!/usr/bin/env python

from setuptools import find_packages, setup


readme = open("README.md").read()
requirements = open("requirements.txt").read().splitlines()

# Define development requirements
dev_requires = [
    "pre-commit>=3.8.0",
    "flake8>=3.9.2",
    "isort>=5.13.2",
    "bandit>=1.7.9",
    "pytest>=6.2.4",
    "pytest-mock>=3.6.1",
    "coverage>=5.5",
    "pip-tools>=6.2.0", 
]

setup(
    name='gd-obsd-common',
    version='1.0',  # This will be updated by the pipeline
    description='Common functionality shared across OBSD streams',
    long_description=readme,
    long_description_content_type="text/markdown",
    author='Saqib Tamli',
    author_email='<EMAIL>',
    url="https://dev.azure.com/ADGOV/GovDigital/_git/gd-obsd-common",
    packages=find_packages(include=["gd_obsd_common", "gd_obsd_common.*"]),
    include_package_data=True,
    install_requires=requirements,
    setup_requires=[],
    tests_require=dev_requires,
    extras_require={
        "dev": dev_requires
    },
    zip_safe=False,
    classifiers=[
        "Programming Language :: Python :: 3",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
    ],
    python_requires='>=3.11',
)

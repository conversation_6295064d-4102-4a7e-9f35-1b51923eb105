trigger:
  - main

variables:
  - name: applicationCode
    value: "DATA-PLATFORM"
  - name: pythonVersion
    value: '3.12'
  - name: packageName
    value: 'gd-obsd-common'

stages:
  - stage: Build
    displayName: 'Build and Test'
    variables:
      - group: ${{ variables.applicationCode }}-INFRA-DEV
    pool:
      name: $(AGENTPOOL)

    jobs:
      - job: BuildAndTest
        displayName: 'Build and Test Package'
        
        steps:
          
        - checkout: self
          fetchTags: true
          fetchDepth: 2   # Fetch the last 2 commits
          persistCredentials: true
          displayName: 'Fetching tags from remote'

        - task: PowerShell@2
          displayName: "Generate Tag"
          name: SetVersion
          inputs:
            targetType: 'inline'
            script: |
              # Fetch all tags from remote
              git fetch --prune --prune-tags
              
              # Get latest tag matching 'v*' pattern, sorted by version
              $latestTag = git tag --list "v*" --sort=-v:refname | Select-Object -First 1
              Write-Output "Found tag: $($latestTag -or 'NONE')"

              Write-Output "Latest tag found: $latestTag"
        
              if (-not ($latestTag -match '^v\d+\.\d+$')) {
                  Write-Host "No valid or Incorrect tag format found. Using v1.0"
                  $version = [pscustomobject]@{ Major = 1; Minor = 0 }
              } else {
                  $latestTag = $latestTag.Trim()
                  $versionParts = $latestTag.TrimStart('v') -split '\.'
                  $major = [int]$versionParts[0]
                  $minor = [int]$versionParts[1]
                  
                  # Increment minor version
                  $minor += 1
                  
                  # If minor reaches 100, increment major and reset minor
                  if ($minor -eq 100) {
                      $major += 1
                      $minor = 0
                  }
                  
                  $version = [pscustomobject]@{
                      Major = $major
                      Minor = $minor
                  }
              }
              
              $newTag = "v$($version.Major).$($version.Minor)"
              Write-Host "##[section]New Tag: $newTag"
              
              # Set the tag as an output variable
              Write-Host "##vso[task.setvariable variable=TAG_VERSION;isOutput=true]$newTag"
              
              # Create and push tag
              git tag -a $newTag -m "Release $newTag"
              git push origin $newTag
              # git push https://$(System.AccessToken)@dev.azure.com/org/project/_git/repo $newTag              
          env:
            SYSTEM_ACCESSTOKEN: $(System.AccessToken)

        - task: UsePythonVersion@0
          inputs:
            versionSpec: '$(pythonVersion)'
            addToPath: true
            architecture: 'x64'
          displayName: 'Using Python $(pythonVersion)'

        - script: |
            python -m pip install --upgrade pip
            python -m pip --version
          displayName: 'Update and Verify pip installation'
          
        # - task: PowerShell@2
        #   inputs:
        #     targetType: 'inline'
        #     script: |
        #       # Update setup.py
        #       $setupContent = Get-Content setup.py -Raw
        #       $setupContent -match "version='([^']+)'" | Out-Null
        #       $packageVersion = $Matches[1]
        #       Write-Host "Current setup.py version: $packageVersion"
        #       $updatedSetupContent = $setupContent -replace "version='([^']+)'", "version='$(SetVersion.TAG_VERSION)'"
        #       Set-Content -Path setup.py -Value $updatedSetupContent
      
        #       # Update pyproject.toml
        #       $pyprojectContent = Get-Content pyproject.toml -Raw
        #       $pyprojectContent -match 'version = "([^"]+)"' | Out-Null
        #       $tomlVersion = $Matches[1]
        #       Write-Host "Current pyproject.toml version: $tomlVersion"
        #       $updatedPyprojectContent = $pyprojectContent -replace 'version = "([^"]+)"', "version = ""$(SetVersion.TAG_VERSION)"""
        #       Set-Content -Path pyproject.toml -Value $updatedPyprojectContent
      
        #       # Set version as pipeline variable
        #       Write-Host "##vso[task.setvariable variable=packageVersion]$(SetVersion.TAG_VERSION)"
        #   displayName: 'Update version in package files'
        #   enabled: true

        - task: PowerShell@2
          inputs:
            targetType: 'inline'
            script: |
              python -m pip install --upgrade pip
              pip install -r requirements.txt
              pip install pytest-azurepipelines "setuptools>=70.1" "wheel>=0.42.0,<0.46" build coverage

          displayName: 'Install requirements'

        - task: PowerShell@2
          inputs:
            targetType: 'inline'
            script: |
              # Create build directory if it doesn't exist
              if (-not (Test-Path -Path "build")) {
                  New-Item -ItemType Directory -Path "build"
              }
              
              # Run tests with coverage
              coverage run --source=gd_obsd_common/ --branch -m pytest tests/ --junitxml=build/test.xml -v
              if ($LASTEXITCODE -ne 0) { exit $LASTEXITCODE }
              coverage xml -i -o build/coverage.xml
          displayName: 'Run Python tests'

        - task: PublishTestResults@2
          displayName: "Publish test results into the Azure DevOps UI"
          condition: succeededOrFailed()
          inputs:
            testResultsFiles: '$(System.DefaultWorkingDirectory)/build/test.xml'
            testRunTitle: 'Publish test results for Python $(pythonVersion)'

        - task: PublishCodeCoverageResults@2
          displayName: "Publish code coverage into the Azure DevOps UI"
          condition: succeededOrFailed()
          inputs:
            summaryFileLocation: '$(System.DefaultWorkingDirectory)/build/coverage.xml'

        - script: |
            python -m pip install --upgrade "setuptools>=70.1" "wheel>=0.42.0,<0.46"
            python -m build --no-isolation --config-setting=builddir=.
          displayName: 'Build Python package with setuptools'
          condition: succeeded()  # Only run if tests pass
          
        - task: CopyFiles@2
          inputs:
            sourceFolder: 'dist'
            contents: '**'
            targetFolder: '$(Build.ArtifactStagingDirectory)'
          displayName: 'Copy build artifacts'
          enabled: true

        - task: PublishBuildArtifacts@1
          inputs:
            pathToPublish: '$(Build.ArtifactStagingDirectory)'
            artifactName: 'dist'
          displayName: 'Publish build artifacts'
          enabled: true
            
  - stage: PublishPackage
    displayName: 'Publish To Feed'
    dependsOn: Build
    condition: succeeded()
    variables:
      - group: ${{ variables.applicationCode }}-INFRA-DEV
      - name: TAG_VERSION
        value: $[ stageDependencies.Build.BuildAndTest.outputs['SetVersion.TAG_VERSION'] ]
    pool:
      name: $(AGENTPOOL)
    jobs:
      - job: PublishPackage
        displayName: 'Publish to Feed'
        steps:
        - checkout: none
          
        - task: UsePythonVersion@0
          inputs:
            versionSpec: '$(pythonVersion)'
            addToPath: true
          displayName: 'Using Python $(pythonVersion)'

        - script: |
            python -m pip install --upgrade pip
            python -m pip --version
          displayName: 'Update and Verify pip installation'

        - task: PowerShell@2
          inputs:
            targetType: 'inline'
            script: |
              Write-Host "Would deploy version $(TAG_VERSION) here"
          displayName: 'Display Version'
          
        - task: DownloadBuildArtifacts@0
          inputs:
            buildType: 'current'
            downloadType: 'single'
            artifactName: 'dist'
            downloadPath: '$(System.ArtifactsDirectory)'
          
        - task: PipAuthenticate@1
          displayName: 'Pip Authenticate'
          inputs:
            artifactFeeds: '$(ARTIFACT_FEED)'
          
        - script: |
            pip install twine
          displayName: 'Install publishing tools'
          
        - task: TwineAuthenticate@1
          inputs:
            artifactFeed: 'GovDigital/$(ARTIFACT_FEED)'
          displayName: 'Twine Authenticate'
            
        - script: |
            python -m twine upload -r "$(ARTIFACT_FEED)" --config-file $(PYPIRC_PATH) $(System.ArtifactsDirectory)/dist/*
          displayName: 'Upload package to feed'
          env:
            SYSTEM_ACCESSTOKEN: $(System.AccessToken)
            ARTIFACT_FEED: $(ARTIFACT_FEED)






# import logging
# import requests
# import time
# import pandas as pd
# import json
# from requests.exceptions import RequestException, ConnectionError, Timeout
# from azure.identity import ClientSecretCredential
# from msgraph import GraphServiceClient
# from pyspark.sql import SparkSession, Row, DataFrame
# from pyspark.sql.functions import col, explode
# from pyspark.sql.types import StructType, ArrayType
 
# class SharePoint:
    
#     # Class to interact with SharePoint.
    
#     def __init__(self) -> None:
#         self.hostname = "adgov.sharepoint.com"
#         # Initialize Microsoft Graph client
#         self.credentials = ClientSecretCredential(
#             tenant_id="0cefd05a-5b38-4ce7-96bb-c31e6e251d18",
#             client_id=dbutils.secrets.get(scope="kv-app", key="common-sharepoint-client-id"),
#             client_secret=dbutils.secrets.get(scope="kv-app", key="common-sharepoint-client-secret")
#         )
#         scopes = ['https://graph.microsoft.com/.default']
#         self.graph_client = GraphServiceClient(self.credentials, scopes)
#         # Acquire token right after initialization
#         self._acquire_access_token()
 
#     def _acquire_access_token(self) -> None:
#         """Generate a new access token for using """
#         scopes = ['https://graph.microsoft.com/.default']
#         logging.info("Generate SharePoint access token")
#         self.access_token = self.credentials.get_token(scopes[0]).token
#         logging.info("New access token acquired!")
 
#     # def _make_msgraph_call(self, request_url: str, max_attempts: int = 3) -> requests.Response:
        
#     #     # Makes msgraph api call and checks for errors.
#     #     # The request header is set using an access token, generated from the app registration credentials.
#     #     # The access token is regenerated if expired.
#     #     # :param request_url: msgraph url to perform the request call.
#     #     # :return: request response object.
        
#     #     status_code = -1
#     #     attempt_count = 0
#     #     while status_code != 200:
#     #         attempt_count += 1
#     #         print(f"Calling msgraph for SharePoint in url {request_url}. Attempt number {attempt_count}")
#     #         response = requests.get(request_url, headers={
#     #             "Authorization": f"Bearer {self.access_token}",
#     #             "Accept": "application/json",
#     #             "Content-Type": "application/json"
#     #         }) #, timeout=60)
#     #         status_code = response.status_code
 
#     #         if attempt_count > max_attempts:
#     #             raise Exception(
#     #                 f"SharePoint can't be accessed after {attempt_count} attempts. {status_code}: {response.text}")
 
#     #         if status_code == 401:
#     #             logging.info("Token expired, generating new one.")
#     #             self._acquire_access_token()
#     #         elif status_code == 302:
#     #             logging.info("SharePoint Graph API request failed with 'Service Unavailable', retrying.")
#     #             logging.debug(f"{status_code}: {response.text}")
#     #         elif status_code == 403:
#     #             raise Exception(
#     #                 "Application credentials do not have sufficient privileges to access this resource"
#     #                 f"{status_code}: {response.text}"
#     #             )
#     #         elif status_code == 404:
#     #             # 404 NOT FOUND errors better handled outside this function
#     #             logging.debug(f"Not found resource {request_url}")
#     #             return response
 
#     #     return response    

#     def _make_msgraph_call(self, request_url: str, max_attempts: int = 5, base_delay: float = 2.0) -> requests.Response:
  
#         # Makes Microsoft Graph API call with retry and token renewal.
        
#         # :param request_url: Full MS Graph API URL
#         # :param max_attempts: Max number of attempts
#         # :param base_delay: Initial delay for exponential backoff in seconds
#         # :return: requests.Response object

#         attempt_count = 0

#         while attempt_count < max_attempts:
#             attempt_count += 1
#             try:
#                 logging.info(f"MS Graph call to URL: {request_url}. Attempt {attempt_count}/{max_attempts}")
#                 response = requests.get(
#                     request_url,
#                     headers={
#                         "Authorization": f"Bearer {self.access_token}",
#                         "Accept": "application/json",
#                         "Content-Type": "application/json"
#                     },
#                     timeout=30  # optional: prevent hanging
#                 )

#                 status_code = response.status_code

#                 # Success
#                 if status_code == 200:
#                     return response

#                 # Token expired
#                 elif status_code == 401:
#                     logging.warning("Access token expired. Renewing token.")
#                     self._acquire_access_token()

#                 # Redirect or temporary unavailability
#                 elif status_code in (302, 503):
#                     logging.warning(f"Temporary issue ({status_code}). Retrying...")

#                 # Forbidden: no point retrying
#                 elif status_code == 403:
#                     raise Exception(
#                         f"Access forbidden. Check app permissions. {status_code}: {response.text}"
#                     )

#                 # Not found: handled gracefully
#                 elif status_code == 404:
#                     logging.warning(f"Resource not found: {request_url}")
#                     return response

#                 # Other non-200 responses
#                 else:
#                     logging.warning(f"Received unexpected status code {status_code}: {response.text}")

#             except (ConnectionError, Timeout, RequestException) as e:
#                 logging.warning(f"Request error: {e}. Attempt {attempt_count}/{max_attempts}")

#             # Wait before retrying
#             sleep_time = base_delay * (2 ** (attempt_count - 1))
#             logging.info(f"Waiting {sleep_time:.1f} seconds before retrying...")
#             time.sleep(sleep_time)

#         # Final failure
#         raise Exception(f"MS Graph call to {request_url} failed after {max_attempts} attempts.")   
 
#     def _get_site_id(self, url: str) -> str:
        
#         # Retrieve site id from the given SharePoint url.
#         # :param url: Url to extract the site from.
#         # :return: site id
      
#         # Extract site name from url
#         if '/teams/' in url:
#             site_name = url.split('/teams/')[1].split('/')[0]
#             endpoint = f":/teams/{site_name}"  # Teams sites require :/teams/ in endpoint
#         elif '/sites/' in url:
#             site_name = url.split('/sites/')[1].split('/')[0]
#             endpoint = f":/sites/{site_name}"  # Site sites require :/teams/ in endpoint
#         else:
#             endpoint = ''  # Default site
#         url = f"https://graph.microsoft.com/v1.0/sites/{self.hostname}{endpoint}"
#         response = self._make_msgraph_call(url)
#         site_id = response.json()["id"]
#         logging.info(f"Retrieved site id '{site_id}' from host '{self.hostname}' and site '{site_name}'")

#         return site_id
 
#     def read_file_from_url(self, url: str) -> bytes:
        
#         # Read SharePoint file content as bytes from the given url.
#         # :param url: SharePoint file's url.
#         # :return: content as bytes.
 
#         site_id = self._get_site_id(url)
 
#         relative_path = url.split(f"/Shared%20Documents/")[1].split('?')[0]
#         request_url = f'https://graph.microsoft.com/v1.0/sites/{site_id}/drive/root:/{relative_path}:/content'
 
#         response = self._make_msgraph_call(request_url)
#         if response.status_code == 404:
#             # If not found return None
#             raise FileNotFoundError(f"File not found in given url {url}. Please review the url and make sure the file is present in the corresponding path. Full error: {response.text}")
#         # Return file content as bytes
#         return response.content
    
#     def get_lists(self, url: str, output_type: str="") -> list:
        
#         # Get all available lists from Sharepoint
        
#         site_id = self._get_site_id(url)
        
#         lists_response = requests.get(f"https://graph.microsoft.com/v1.0/sites/{site_id}/lists",
#                                       headers={
#                                           "Authorization": f"Bearer {self.access_token}",
#                                           "Accept": "application/json",
#                                           "Content-Type": "application/json"
#                                       })
#         lists = lists_response.json()
        
#         if output_type == "table":
#             lists = SharePoint.flatten_df(lists)
#         elif output_type == "json":
#             lists = json.dumps(lists)
#         else:
#             pass
            
#         return lists
    
#     def get_list_content(self, url: str, list_id: str, output_type: str=""):
        
#         # Get list content in dataframe format.

#         def clean_value(value):
#             """
#             Limpia los valores para que sean compatibles con PySpark.
#             """
#             import datetime

#             if isinstance(value, (int, float, bool, str, type(None))):
#                 return value
#             elif isinstance(value, datetime.datetime):
#                 return value.isoformat()
#             elif isinstance(value, list):
#                 return str(value)
#             elif isinstance(value, dict):
#                 return str(value)
#             else:
#                 return str(value)

#         site_id = self._get_site_id(url)

#         list_id = list_id
#         items_response = requests.get(
#             f"https://graph.microsoft.com/v1.0/sites/{site_id}/lists/{list_id}/items?expand=columns,items(expand=fields)",
#             headers={
#                 "Authorization": f"Bearer {self.access_token}",
#                 "Accept": "application/json",
#                 "Content-Type": "application/json"
#             })
        
#         items = items_response.json()
#         data = [item['fields'] for item in items['value']]

#         if output_type == "table":
#             data = SharePoint.flatten_df(data)
#         elif output_type == "json":
#             data = json.dumps(items)
#         else:
#             pass

#         return data
    
# # Function to Transform JSON into Table 
#     @staticmethod
#     def flatten_df(lists: dict) -> DataFrame:
        
#         # Recursively flattens a PySpark DataFrame with structs and arrays,
#         # handling column names with special characters.
        
#         def safe_col(col_name: str):
            
#             # Returns a safe `col` expression for column names with special characters.
            
#             if any(c in col_name for c in ['.', '@', ' '] ):
#                 return col(f"`{col_name}`")
#             else:
#                 return col(col_name)

#         complex_fields = True

#         pdf = pd.json_normalize(lists)
#         df = spark.createDataFrame(pdf)

#         while complex_fields:
#             complex_fields = False
#             new_cols = []
#             for field in df.schema.fields:
#                 field_name = field.name
#                 field_type = field.dataType

#                 if isinstance(field_type, StructType):
#                     # Decompose struct
#                     for subfield in field_type.fields:
#                         sub_name = subfield.name
#                         new_name = f"{field_name}_{sub_name}"
#                         new_cols.append(col(f"`{field_name}`.`{sub_name}`").alias(new_name))
#                     complex_fields = True

#                 elif isinstance(field_type, ArrayType) and isinstance(field_type.elementType, StructType):
#                     # Explode array of structs
#                     df = df.withColumn(field_name, explode(safe_col(field_name)))
#                     complex_fields = True
#                     new_cols.append(safe_col(field_name))

#                 else:
#                     new_cols.append(safe_col(field_name))

#             df = df.select(*new_cols)

#         return df   
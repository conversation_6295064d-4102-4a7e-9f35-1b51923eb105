[build-system]
requires = ["setuptools==68.2.2", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "gd-obsd-common"
version = "1.0.0"
description = "Common functionality shared across OBSD streams"
readme = "README.md"
requires-python = ">=3.11"
license = {text = "MIT"}
authors = [
    {name = "Saqib Tamli", email = "<EMAIL>"}
]
classifiers = [
    "Programming Language :: Python :: 3",
    "Operating System :: OS Independent",
]
dependencies = [
    "pytz",
]

[project.optional-dependencies]
dev = [
    "pre-commit>=3.8.0",
    "flake8>=3.9.2",
    "isort>=5.13.2",
    "bandit>=1.7.9",
    "pytest>=6.2.4",
    "pytest-mock>=3.6.1",
    "coverage>=5.5",
    "pip-tools>=6.2.0"
]

[project.urls]
"Homepage" = "https://dev.azure.com/ADGOV/GovDigital/_git/gd-obsd-common"
"Repository" = "https://dev.azure.com/ADGOV/GovDigital/_git/gd-obsd-common"

[tool.setuptools]
packages = ["gd_obsd_common"]
